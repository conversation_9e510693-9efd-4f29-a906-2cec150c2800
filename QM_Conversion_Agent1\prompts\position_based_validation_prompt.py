"""
Prompts for position-based error identification validation in database conversion.
"""
from typing import Dict

def create_position_based_validation_prompt(target_error_context: Dict, error_message: str) -> str:
    """
    Creates a prompt for validating position-based error statement identification.

    This function creates a prompt that instructs the LLM to validate if the position-based
    identified error statement is indeed causing the specified error.

    Args:
        target_error_context: Dictionary containing the error context (before, error, after statements)
        error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to validate if the position-based identified statement is causing the reported error during Oracle to PostgreSQL migration.

ERROR MESSAGE:
{error_message}

POSITION-BASED IDENTIFIED CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

TASK:
Validate if the position-based identified statement would actually cause the EXACT deployment error reported:
1. Analyze the specific deployment error message components (error type, line/position, specific syntax mentioned)
2. Examine the identified error statement for the exact issues mentioned in the deployment error
3. Determine if this specific statement would trigger this exact deployment error when executed
4. Verify that the error is not caused by other statements in the context

CRITICAL VALIDATION APPROACH:
- DEPLOYMENT ERROR FOCUS: Check if the identified statement contains the EXACT syntax/issue mentioned in the deployment error
- ERROR MESSAGE CORRELATION: Look for direct references between error message details and statement content
- EXECUTION SIMULATION: Determine if executing this statement would produce this specific deployment error
- POSITION VERIFICATION: Confirm the error position/line actually points to problematic syntax in this statement
- ALTERNATIVE ANALYSIS: Consider if the error could actually be caused by before/after statements instead

DEPLOYMENT ERROR ANALYSIS CRITERIA:
- EXACT ERROR MATCH: Does the identified statement contain the specific syntax/element mentioned in the deployment error?
- ERROR TYPE CORRELATION: Does the statement's database operation type match the error category (syntax, constraint, function, etc.)?
- POSITION ACCURACY: Does the error line/position information correctly map to problematic syntax in this statement?
- EXECUTION CAUSALITY: Would executing this statement actually trigger this specific deployment error?
- CONTEXT ELIMINATION: Can you rule out that before/after statements are the actual cause of this deployment error?

ERROR PATTERN ANALYSIS:
- Data Type Issues: NUMBER vs NUMERIC/INTEGER, VARCHAR2 vs VARCHAR, DATE formatting
- Syntax Differences: Oracle-specific functions, operators, keywords, PL/SQL constructs
- Constraint Violations: NOT NULL, CHECK, FOREIGN KEY, UNIQUE constraint errors
- Function/Procedure Issues: Parameter handling, RETURN statements, OUT parameters
- Control Flow Issues: IF/ELSE, LOOP, EXCEPTION handling syntax differences
- Transaction Issues: COMMIT, ROLLBACK, SAVEPOINT handling differences
- Cursor Operations: OPEN, FETCH, CLOSE cursor statement problems
- Aggregate/Window Functions: GROUP BY, HAVING, analytical function errors

DEPLOYMENT ERROR VALIDATION DECISION:
- HIGH CONFIDENCE (0.8-1.0): Statement contains exact syntax/issue from deployment error AND position mapping is accurate
- MEDIUM CONFIDENCE (0.5-0.79): Statement likely causes this deployment error with reasonable position correlation
- LOW CONFIDENCE (0.2-0.49): Statement might cause error but correlation is weak or position mapping questionable
- VERY LOW CONFIDENCE (0.0-0.19): Statement unlikely to cause this specific deployment error OR position mapping is incorrect

VALIDATION RESULT GUIDELINES:
- Return TRUE only if you can clearly identify how this statement would cause this EXACT deployment error
- Return FALSE if the deployment error seems unrelated to this statement or if another statement is more likely the cause
- Focus on the specific error message details, not general Oracle-to-PostgreSQL conversion issues

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<deployment error validation analysis including: 1) EXACT ERROR CORRELATION - specific elements from deployment error found in the statement, 2) POSITION MAPPING ACCURACY - how error line/position maps to statement content, 3) EXECUTION CAUSALITY - detailed explanation of how this statement would cause this specific deployment error, 4) STATEMENT ANALYSIS - line-by-line examination of problematic syntax/elements, 5) CONTEXT EVALUATION - analysis of whether before/after statements could be the actual cause, 6) ALTERNATIVE ASSESSMENT - if validation fails, suggest which statement might actually cause this deployment error, 7) TECHNICAL EVIDENCE - specific database operation details that support the validation decision>"
}}

CRITICAL FOCUS: Your validation must determine if this specific statement would cause this exact deployment error when executed in PostgreSQL. Be precise and evidence-based in your analysis."""
