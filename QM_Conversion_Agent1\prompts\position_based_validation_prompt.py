"""
Prompts for position-based error identification validation in database conversion.
"""
from typing import Dict

def create_position_based_validation_prompt(target_error_context: Dict, error_message: str) -> str:
    """
    Creates a simple validation prompt for position-based error identification.

    This function creates a prompt that instructs the LLM to perform simple YES/NO validation
    focused ONLY on deployment error causality. It ignores conversion quality, optimization,
    or alternatives to prevent over-analysis.

    Args:
        target_error_context: Dictionary containing the error context (before, error, after statements)
        error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM focused on deployment error causality only
    """
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to validate if the position-based identified statement is causing the reported error during Oracle to PostgreSQL migration.

ERROR MESSAGE:
{error_message}

POSITION-BASED IDENTIFIED CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

TASK:
Validate if the position-based identified statement would cause the EXACT deployment error when executed in PostgreSQL:
1. Check if the deployment error message directly points to syntax/issues in this specific statement
2. Determine if executing this statement in PostgreSQL would produce this exact deployment error
3. Verify the error position/line maps correctly to this statement
4. Simple YES/NO validation - does this statement cause this deployment error?

SIMPLE VALIDATION APPROACH:
- DEPLOYMENT ERROR MATCH: Does this statement contain the exact syntax/issue that PostgreSQL is complaining about?
- EXECUTION CHECK: Would running this statement in PostgreSQL produce this exact error message?
- POSITION CHECK: Does the error line/position correctly point to this statement?
- DIRECT CAUSALITY: Is this statement the direct cause of the deployment failure?

SIMPLE VALIDATION CRITERIA:
- SYNTAX MATCH: Does this statement have the exact syntax issue mentioned in the deployment error?
- ERROR LOCATION: Does the error line/position point to this statement?
- DIRECT CAUSE: Would this statement directly cause this deployment error when executed?
- NOT OTHER STATEMENTS: Are you confident the error is NOT from before/after statements?

FOCUS ONLY ON:
- Does this statement have the exact problem mentioned in the deployment error?
- Would PostgreSQL throw this exact error when executing this statement?
- Does the error position point to this statement?

IGNORE:
- Oracle-to-PostgreSQL conversion quality
- Code optimization or best practices
- Alternative approaches or improvements
- General syntax issues not mentioned in the deployment error

VALIDATION DECISION:
- HIGH CONFIDENCE (0.8-1.0): This statement clearly causes this exact deployment error
- MEDIUM CONFIDENCE (0.5-0.79): This statement likely causes this deployment error
- LOW CONFIDENCE (0.2-0.49): Uncertain if this statement causes this deployment error
- VERY LOW CONFIDENCE (0.0-0.19): This statement does NOT cause this deployment error

SIMPLE GUIDELINES:
- Return TRUE only if this statement directly causes this deployment error
- Return FALSE if the deployment error is NOT caused by this statement
- Focus ONLY on deployment error causality, nothing else

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<simple deployment error validation: 1) EXACT ERROR MATCH - does this statement have the syntax issue mentioned in deployment error, 2) POSITION CHECK - does error line/position point to this statement, 3) DIRECT CAUSALITY - would executing this statement cause this exact deployment error, 4) SIMPLE CONCLUSION - yes/no with brief reasoning>"
}}

CRITICAL FOCUS: Only validate if this statement causes this deployment error. Do NOT analyze conversion quality, optimization, or alternatives."""
